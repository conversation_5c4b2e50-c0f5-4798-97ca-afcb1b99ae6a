<template>
	<link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<link rel="shortcut icon" href="/favicon.ico" />
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
	<meta name="apple-mobile-web-app-title" content="TZH" />
	<link rel="manifest" href="/site.webmanifest" />

	<Body :class="{'fixed-header': showElement && y > 270 && !disableFixed, 'active-nav': activeNav}" />
	<div class="page-wrapper">
		<ClientOnly>
			<BaseThemeUiPageLoadingIndicator track-color="#ABC075" bar-color="#809941" />
		</ClientOnly>
		<CmsLayoutAboveHeader />

		<CmsHeader v-if="showElement">
			<template #navBtn>
				<div class="btn-toggle-nav" :class="{'active': activeNav}" v-if="mobileBreakpoint" @click="activeNav = !activeNav"><span></span></div>
			</template>

			<template #afterHeader>
				<template v-if="isSearch">
					<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish|01'}, {module: 'publish|02'}, {module: 'cms'}]" v-slot="{searchTerm}">
						<SearchResultsHeader :search-term="searchTerm" />
					</BaseSearchResults>
				</template>
				<CmsHomepageIntro v-if="isHomepage" />
				<CatalogIndexHeader v-if="showCategoriesWidget && !isQuickOrder" />
				<CatalogCategoriesWidget :extraclass="'categories c-categories'" v-if="showCategoriesWidget && !isQuickOrder" />
				<div class="wrapper quickorder-header" v-if="isQuickOrder">
					<BaseCmsLabel code="quick_order" tag="h1" class="title-quickorder" />
				</div>
				<PublishPostHeader v-if="isPublishDetail" />
				<PublishIndexHeader v-if="isPublishIndex && !isSearch" />
				<CatalogIndexWishlistHeader v-if="isIndexWishlistHeader" />
				<WebshopCartHeader v-if="isWebshopCartHeader" />
			</template>
		</CmsHeader>

		<slot />

		<CmsLayoutElements :instashopData="instashopData" :showElement="showElement" />
	</div>
</template>

<script setup>
	const route = useRoute();
	const {onMediaQuery, onScroll, scrollTo} = useDom();
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1250px)'});
	const {matches: tabletSmallBreakpoint} = onMediaQuery({query: '(max-width: 1030px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 990px)'});
	const {matches: mobileBreakpointInstashop} = onMediaQuery({query: '(max-width: 800px)'});
	provide('rwd', {mobileBreakpoint, tabletBreakpoint, tabletSmallBreakpoint, mobileBreakpointInstashop});

	const instashopData = useState('instashopData');

	// Workaround to move publish detail header to header slot
	const isPublishDetail = computed(() => {
		return route.meta.template === 'PublishDetail' || route.meta.template === 'PublishRecipesDetail' ? true : false;
	});

	// Workaround to move recipes index header to header slot
	const isPublishIndex = computed(() => {
		return route.meta.template === 'PublishRecipes' || route.meta.template === 'PublishAuthorDetail' ? true : false;
	});

	// Workaround to move wishlist header to header slot
	const isIndexWishlistHeader = computed(() => {
		return route.meta.template === 'CatalogWishlist' ? true : false;
	});

	// Workaround to move cart header to header slot
	const isWebshopCartHeader = computed(() => {
		return route.meta.template === 'WebshopShoppingCart' ? true : false;
	});

	const showCategoriesWidget = computed(() => {
		return route.meta.controller == 'catalog' && route.meta.contentType == 'category' && route.params.slug.length == 2 ? true : false;
	});

	const isHomepage = computed(() => {
		return route.meta.template === 'CmsHomepage' ? true : false;
	});

	/*const isSearch = computed(() => {
		return route.query.search_q  ? true : false;
	});*/
	const isSearch = computed(() => !!route.query.search_q && route.meta.template != 'CatalogDetail');

	const isQuickOrder = computed(() => {
		return route.query.special_view === 'order_form' ? true : false;
	});

	const showElement = computed(() => {
		if(!route?.meta?.action) return true;
		return (route.meta.controller == 'webshop' && ['login', 'customer', 'shipping', 'payment', 'review_order'].includes(route.meta.action)) ? false : true;
	});

	const disableFixed = computed(() => {
		if(!route?.meta?.action) return true;
		return (route.meta.controller != 'staticcontent') ? false : true;
	});

	const activeNav = ref(false);
	const {y} = onScroll({
		debounce: 150,
	});

	watch(
		() => route.fullPath,
		(newPath, oldPath) => {
			activeNav.value = false;
		}
	);
</script>
