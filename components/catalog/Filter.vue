<template>
	<div class="cf-item" :id="index+1" ref="filterItem" :class="['cf-item-' + filter.code, filter.layout ? 'cf-layout-' + filter.layout : '', {'active': selectedFilter}]" v-if="filter.layout == 'sf' || filter.options && !filter.special_filter">
		<div class="cf-item-title-cnt" :class="{'empty-filter': !selectedFilters?.length}" @click="toggleFilter">
			<span class="cf-item-title">{{ filter.label }}</span>
			<span class="cf-filters-counter" v-show="selectedFilters?.length">{{selectedFilters?.length}}</span>
			<span class="icon-dropdown" @click="onToggle"></span>
		</div>
		<div class="cf-item-wrapper" :class="{'cf-item-wrapper-brand': filter.code == 'manufacturer', 'price': filter.code == 'price', 'cf-item-wrapper-model': filter.code == 'a_podrzani-model'}">
			<template v-if="filter.layout == 'sf'">
				<BaseCatalogFilterFieldSlider :item="filter" :auto-submit="true" />
			</template>
			<template v-if="filter.code == 'brand'">
				<div class="cf-item-wrapper-inner manufacturer">
					<div class="cf-row" :class="[field.level && 'cf-row-level' + field.level, {'not-available': field.total_available < 1 && !field.selected}]" v-for="field in fields" :key="field.id">
						<input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
						<label :for="field.unique_code">
							<div class="cf-manufacturer-img" v-if="field?.image_upload_path" :class="field.code">
								<BaseUiImage :src="field.image_upload_path" width="83" height="16" alt="" default="/images/no-image-50.jpg" />
							</div>
							<span class="cf-img-title" v-else>{{field.title}}</span>
						</label>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="cf-item-wrapper-inner" :class="{'custom': fields?.length > 8 && !filter.allOptions, 'show-all': filter.allOptions, 'manufacturer': filter.code == 'manufacturers'}" v-if="filter.layout != 'sf'">
					<div class="cf-row" :class="[field.level && 'cf-row-level' + field.level, {'not-available': field.total_available < 1 && !field.selected, 'manufacturer': filter.code == 'manufacturers'}]" v-for="field in fields" :key="field.id">
						<input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
						<label :for="field.unique_code">
							<span>{{field.title}}</span>
						</label>
					</div>
				</div>
			</template>
			<span v-show="selectedFilters?.length" class="cf-item-filters-remove" @click="onClear"><BaseCmsLabel code="remove_filters" tag="span" /></span>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['filter', 'index', 'fields', 'onFilter', 'onClear', 'selectedFilters', 'active', 'onToggle', 'onSearch']);
	const labels = useLabels();
	const {onClickOutside} = useDom();
	const {mobileBreakpoint} = inject('rwd');

	let selectedFilter = ref(false);
	const filterItem = ref(null);

	function toggleFilter(){
		selectedFilter.value = !selectedFilter.value;
	};

	onClickOutside(filterItem, () => {
		if(!mobileBreakpoint.value){
			selectedFilter.value = false;
		}
	});
</script>

<style lang="less" scoped>
	.cf-item{
		position: relative;
		&.active{
			.cf-item-wrapper{display: block;}
			.cf-item-title-cnt{border-color: @lightGreen;}
		}
		@media (max-width: @tp){
			width: 100%; border: 0; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; margin-top: -1px!important;
			&.active{
				.cf-item-title-cnt .icon-dropdown:after{display: none;}
			}
		}
	}
	.cf-item-title-cnt{
		height: 50px; border: 1px solid #DEDEDE; border-radius: 2px; padding: 0 40px 0 20px; font-size: 14px; display: flex; align-items: center; background: url(assets/images/icons/arrow-down.svg) center right 20px; background-repeat: no-repeat; background-size: 12px;
		@media (max-width: @tp){
			border: none; font-weight: bold; background: none; padding: 0 52px 0 16px; position: relative;
			.icon-dropdown{
				position: absolute; display: flex; align-items: center; justify-content: center; right: 16px; width: 14px; height: 14px;
				&:before{.pseudo(14px,2px); background: @lightGreen;}
				&:after{.pseudo(2px,14px); background: @lightGreen;}
			}
		}
	}
	.cf-filters-counter{position: relative; display: flex; align-items: center; justify-content: center; width: 22px; height: 22px; border-radius: 50%; background: @lightGreen; color: #fff; font-size: 12px; line-height: 1; font-weight: 700; margin-left: 5px; text-indent: -2px;}
	.cf-item-wrapper{
		position: absolute; top: calc(~"100% - -10px"); z-index: 100; display: none; background: #fff; box-shadow: 0 5px 20px rgba(0,0,0,0.15);
		&:after{.pseudo(10px,10px); background: #fff; .rotate(45deg); top: -4px; left: 20px;}
		@media (max-width: @tp){
			position: relative; top: auto; box-shadow: none;
			&:after{display: none;}
		}
	}
	.cf-item-wrapper-inner{
		position: relative; display: flex; flex-flow: column;  padding: 20px;  gap: 8px; max-height: 160px; overflow-y: auto;
		&:after{.pseudo(10px,10px); background: #fff; .rotate(45deg); top: -4px; left: 20px;}
		&.manufacturer{flex-flow: inherit; flex-wrap: wrap; width: 460px;}
		@media (max-width: @tp){
			padding: 0; gap: 0; max-height: 640px; overflow-y: auto;
			&:after{display: none;}
			&.manufacturer{width: 100%; gap: 8px;}
		}
	}
	.cf-row{
		input[type=checkbox]+label{white-space: nowrap; font-size: 12px; width: 100%; padding-right: 30px;}
		input[type=checkbox]:checked+label{font-weight: bold;}
		&.manufacturer{
			input[type=checkbox]+label{
				padding: 6px 12px; border: 1px solid #DEDEDE; border-radius: 2px;
				&:before{display: none;}
				@media (min-width: @h){
					&:hover{border-color: @lightGreen;}
				}
			}
			input[type=checkbox]:checked+label{background: @lightGreen; border-color: @lightGreen; color: #fff; font-weight: normal;}
		}
	}
	:deep(.cf-range){
		position: relative; display: flex; flex-flow: column; padding: 20px; width: 395px;
		//&:after{.pseudo(10px,10px); background: #fff; .rotate(45deg); top: -4px; left: 20px;}
		@media (max-width: @tp){width: calc(~"100% - 16px"); padding: 0; margin: 0 auto;}
	}
	:deep(.cf-range-input){
		display: flex; gap: 10px; align-items: center; justify-content: center; margin-bottom: 20px;
		input{width: 100px; height: 34px; text-align: center; padding: 0 10px;}
		@media (max-with: @tp){margin-bottom: 16px;}
	}
	:deep(.slider-connects){background: rgba(171, 192, 117, 0.1);}
	:deep(.slider-connect){background: @lightGreen;}
	:deep(.slider-horizontal .slider-handle){width: 20px; height: 20px; top: calc((var(--slider-handle-height, 16px) - var(--slider-height, 6px))/2*-1 + -3px)}
	.cf-item-filters-remove{
		position: relative; display: flex; align-items: center; justify-content: center; border-top: 1px solid #DEDEDE; height: 50px;
		span{position: relative; display: flex; font-size: 14px; line-height: 1; align-items: center;}
		@media (max-width: @tp){display: none;}
	}
</style>
