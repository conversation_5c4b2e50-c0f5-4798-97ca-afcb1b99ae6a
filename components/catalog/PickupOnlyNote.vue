<template>
	<template v-if="item?.type?.length && item.type == 'pickup'">
		<div class="cd-item-status-stores">
			<BaseCmsLabel code="pickup_info" tag="div" class="status-info-label" />
			<BaseCmsLabel code="available_only_in_office" :replace="[{'%COUNT%': warehousesArray.length, '%LOCATIONS%': warehousesArray}]" tag="div" />
		</div>
	</template>
</template>

<script setup>
	const props = defineProps(['item']);
	const warehousesIds = props.item.warehouses_ids;
	const warehouses = props.item.warehouses;
	const warehousesCount = ref(0);
	const warehousesArray = [];

	const pairs = warehousesIds.slice(1).split(",")
	const warehouseIds = pairs
		.filter(pair => {
			const [key, value] = pair.split("=");
			return key && value && Number(value) > 0;
		})
		.map(pair => Number(pair.split("=")[0]));

	warehouses?.forEach(warehouse => {
		const warehouseId = Number(warehouse.id);
		if (warehouseIds.includes(warehouseId)) {
			warehousesArray.push(warehouse.title);
		}
	});
</script>
