<template>
	<Body class="page-auth" />
	<ClientOnly>
		<BaseAuthUser>
			<CmsTwoColumns>
				<template #main>
					<div class="fg1 lists main-content">
						<slot name="authContent" />
					</div>
				</template>

				<template #sidebar>
					<aside class="sidebar">
						<div class="a-sidebar-cnt">
							<BaseAuthUser v-slot="{user, urls, currentUrl}">
								<div v-if="user?.first_name" class="a-dashboard-title">
									<BaseCmsLabel code="welcome" />, <span>{{user.first_name}}</span>
								</div>
								<ul class="nav-sidebar nav-auth">
									<li class="auth-profile" :class="{'selected': urls.auth == currentUrl}">
										<NuxtLink :to="urls.auth"><BaseCmsLabel code="my_profile" tag="span" /></NuxtLink>
									</li>
									<li class="auth-orders" :class="{'selected': urls.auth_my_webshoporder == currentUrl}">
										<NuxtLink :to="urls.auth_my_webshoporder"><BaseCmsLabel code="my_orders" tag="span" /></NuxtLink>
									</li>
									<li class="auth-wishlist" :class="{'selected': urls.auth_wishlist == currentUrl}">
										<NuxtLink :to="urls.auth_wishlist"><BaseCmsLabel code="my_wishlist" tag="span" /></NuxtLink>
									</li>
									<li class="auth-coupons" :class="{'selected': urls.auth_my_webshopcoupon == currentUrl}" v-if="!b2b">
										<NuxtLink :to="urls.auth_my_webshopcoupon"><BaseCmsLabel code="my_coupons" tag="span" /></NuxtLink>
									</li>
									<li class="auth-edit" :class="{'selected': urls.auth_edit == currentUrl}">
										<NuxtLink :to="urls.auth_edit"><BaseCmsLabel code="edit_profile" tag="span" /></NuxtLink>
									</li>
									<li class="auth-password" :class="{'selected': urls.auth_change_password == currentUrl}">
										<NuxtLink :to="urls.auth_change_password"><BaseCmsLabel code="change_password" tag="span" /></NuxtLink>
									</li>
									<li class="auth-logout">
										<NuxtLink :to="urls.auth_logout+'?redirect=/'"><BaseCmsLabel code="logout" tag="span" /></NuxtLink>
									</li>
								</ul>
							</BaseAuthUser>
						</div>
						<div class="auth-box-loyalty" :class="['has-loyalty-card', 'no-loyalty-card' && !loyalty?.code]" v-if="!b2b">
							<div class="auth-loyalty-card">
								<BaseCmsLanguages v-slot="{currentLanguage}">
									<template v-if="currentLanguage.lang == 'si'">
										<img src="/assets/images/card-small-si.png" width="129" height="80" alt="" />
									</template>
									<template v-else-if="currentLanguage.lang == 'de'">
										<img src="/assets/images/card-small-de.png" width="129" height="80" alt="" />
									</template>
									<template v-else-if="currentLanguage.lang == 'en'">
										<img src="/assets/images/card-small-en.png" width="129" height="80" alt="" />
									</template>
									<template v-else>
										<img src="/assets/images/card-small-hr.png" width="129" height="80" alt="" />
									</template>
								</BaseCmsLanguages>
							</div>
							<div v-if="loyalty?.code" class="a-intro-loyalty-cnt">
								<div class="auth-loyalty-cnt">
									<p>
										<BaseCmsLabel tag="span" class="label" code="card_number" />: <span class="value">{{loyalty.code}}</span>
									</p>
									<p v-if="loyalty.points?.available">
										<BaseCmsLabel tag="span" class="label" code="loyalty_points" />: <span class="value">{{loyalty.points.available}}</span>
									</p>
									<p v-if="loyalty.discount_percent">
										<BaseCmsLabel tag="span" class="label" code="loyalty_discount" />: <span class="value">{{loyalty.discount_percent}}%</span>
									</p>
									<BaseCmsLabel tag="span" code="about_loyalty" v-interpolation />
								</div>
							</div>
							<div v-else class="auth-loyalty-nocard">
								<BaseCmsLabel class="auth-title-loyalty" tag="div" id="loyalty_code_request" code="auth_loyalty_title_nocard" />
								<BaseCmsLabel class="auth-loyalty-cnt" tag="div" code="auth_loyalty_subtitle" />

								<BaseForm v-if="!loyaltyStatus?.success" @submit="onSubmit">
									<div class="auth-loyalty-field-container">
										<div class="auth-loyalty-btns" v-interpolation>
											<a class="btn btn-loyalty" @click.prevent="onRequestNewCard"
												><span><UiLoader v-if="loyaltyLoading" /><BaseCmsLabel v-if="!loyaltyLoading" code="request_new_card" /></span
											></a>
											<BaseCmsLabel tag="span" code="about_loyalty" />
										</div>

										<BaseFormField v-for="item in formFields" :key="item.name" :item="item" v-slot="{errorMessage}">
											<label for="loyalty_code"><BaseCmsLabel code="add_loyalty_card" /></label>
											<div class="auth-loyalty-field-card">
												<BaseFormInput :id="item.name" />
												<button type="submit" class="btn btn-orange btn-loyalty btn-loyalty-add"><UiLoader v-if="loyaltyLoading" /><BaseCmsLabel v-if="!loyaltyLoading" code="attach_card" /></button>
												<span class="error" v-show="errorMessage" v-html="errorMessage" />
												<span v-if="!loyaltyStatus?.success && loyaltyStatus.data?.label_name" class="error">{{loyaltyStatus.data.label_name}}</span>
											</div>
										</BaseFormField>
									</div>
								</BaseForm>
								<div v-if="loyaltyStatus.data?.label_name == 'success_saved_loyalty_code'" class="global-success auth-loyalty-success"><BaseCmsLabel code="auth_loyalty_attach_card" /></div>
							</div>
						</div>
					</aside>
				</template>
			</CmsTwoColumns>

			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</BaseAuthUser>
	</ClientOnly>
</template>

<script setup>
	const {mobileBreakpoint} = inject('rwd');
	const {b2b} = useProfile();
	const {scrollTo} = useDom();
	const loyaltyLoading = ref(false);
	const loyaltyStatus = ref({});
	const endpoints = useEndpoints();

	const formFields = [{
		"name": "loyalty_code",
		"type": "text",
		"validation": [
			{
				"type": "not_empty",
				"value": null,
				"error": "error_not_empty"
			},
			{
				"type": "max_length",
				"value": 45,
				"error": "error_max_length"
			}
		],
	}]

	const {getCartData, fetchCart} = useWebshop();
	const loyalty = computed(() => {
		return getCartData()?.cart?.loyalty ? getCartData()?.cart?.loyalty : false;
	});


	async function onRequestNewCard() {
		loyaltyStatus.value = {};
		loyaltyLoading.value = true;

		loyaltyStatus.value = await useApi(endpoints.get('_post_hapi_auth_loyalty'), {
			method: 'POST',
			body: { loyalty_request: 'n', loyalty_code: '' },
		});

		if (!loyaltyStatus.value.success) {
			loyaltyStatus.value = { data: { label_name: 'Greška kod zahtjeva za novu karticu' } };
			loyaltyLoading.value = false;
			return;
		}

		await new Promise(resolve => setTimeout(resolve, 2000));
		await fetchCart();

		loyaltyStatus.value = {};
		loyaltyLoading.value = false;
	}

	async function onSubmit({values}) {
		loyaltyStatus.value = {};
		loyaltyLoading.value = true;

		loyaltyStatus.value = await useApi(endpoints.get('_post_hapi_auth_loyalty'), {
			method: 'POST',
			body: {loyalty_request: 'e', loyalty_code: values.loyalty_code},
		});

		/* FIXME - potrebno prevesti za više jezika labelu (preuzeto s ljekarne-prime-farmacie */
		if(!loyaltyStatus.value.success){
			loyaltyStatus.value = {data: {label_name: 'Greška kod pridruživanja kartice'}};
			return loyaltyLoading.value = false;
		}

		await new Promise(resolve => setTimeout(resolve, 2000));

		await fetchCart();

		loyaltyStatus.value = {};
		loyaltyLoading.value = false;
	}
</script>
