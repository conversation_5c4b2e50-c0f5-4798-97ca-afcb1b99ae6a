<template>
	<!-- FIXME INTEG - Dodati LAZY isped CmsNewsletter dijela kasnije -->
	<CmsNewsletter hydrate-never v-if="showNewsletter" />

	<LazyCmsBottom v-if="showElement" />

	<LazyCmsFooter />

	<ClientOnly>
		<LazyBaseThemeUiModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" :zoom="false" :mask-closable="true" :svgicons="false" />
		<NewsletterLeaving />
		<LazyBaseThemeUiAdminBar v-if="user?.staff || user?.superuser || user?.developer" />
	</ClientOnly>

	<PublishInstashopModal :instashopData="instashopData" />

	<UiOntop />
</template>

<script setup>
	const {user} = useAuth();
	const modal = useModal();
	const route = useRoute();
	const {getInfo} = useInfo();
	const {addScript, waitForWindowProperty} = useMeta();
	const lang = useLang();
	const url = useUrl();
	const {prependTo, appendTo, insertAfter, insertBefore, onMediaQuery} = useDom();
	const props = defineProps(['instashopData', 'showElement']);
	const showNewsletter = computed(() => {
		if (!route?.meta?.template || !route?.meta?.contentType) return true;
		if (!route?.meta?.action) return true;

		if (route.meta.controller === 'webshop') {
			const actionsToHide = ['login', 'customer', 'shipping', 'payment', 'review_order'];
			return !actionsToHide.includes(route.meta.action);
		}

		if (route.meta.contentType === 'auth') {
			const actionsToHide = ['AuthDefault','AuthMyWebshoporder', 'AuthWishlist', 'AuthMyWebshopcoupon', 'AuthLogin', 'AuthSignup', 'AuthForgottenPassword', 'AuthEdit', 'AuthChangePassword'];
			return !actionsToHide.includes(route.meta.template);
		}
		return true;
	});

	/* const {onMediaQuery, appendTo, insertAfter} = useDom();
	onMediaQuery({
		query: '(max-width: 950px)',
		enter() {
			appendTo('.pw-btns', '.pw-recipes-col1');
		},
		leave() {
			insertAfter('.pw-btns', '.pw-recipe-items');
		},
	}); */


	onMediaQuery({
		query: '(max-width: 950px)',
		timeout: 600,
		enter() {
			appendTo('.pw-btns', '.pw-recipes-col1');
			insertAfter('.pd-recipe-related-products', '#comments');
			appendTo('.ww-coupons-cart, .free-delivery-container', '.w-col2-cnt-top');
			insertBefore('.qo-col-cnt-desc', '.qo-col1');
			insertBefore('.m-logo', '.c-title');
			appendTo('.auth-box-loyalty', '.main-content');
			appendTo('.page-recipes .cf-active', '.p-recipes-header');
			insertAfter('.ww-coupons-preview', '.ww-preview-header');

		},
		leave: () => {
			insertAfter('.pw-btns', '.pw-recipe-items');
			insertAfter('.pd-recipe-related-products', '.pd-recipe-sidebar-top');
			insertBefore('.ww-coupons-cart', '.cart-totals');
			insertBefore('.qo-col-cnt-desc', 'qo-col-cnt-totals');
			insertBefore('.m-logo', '.cf-products');
			appendTo('.auth-box-loyalty', 'sidebar');
			prependTo('.page-recipes .cf-active', '.cf-body');
			//window.location.reload();

		},
	});

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 900px)',
		timeout: 600,
		enter: () => {
			appendTo('.page-homepage .sw', '.page-homepage .sw-placeholder');
			prependTo('.filters-container', '.page-wrapper');
			insertAfter('.cf-special', '.c-toolbar');
			insertAfter('.pd-header', '.header');
			insertBefore('.pd-info', '.pd-title');
			appendTo('.pd-info-link-comments', '.pd-info-feedback');
			insertAfter('.pd-recipe-header-wrapper', '.header');
			appendTo('.pd-recipe-info', '.pd-recipe-info-container');
			appendTo('.pd-recipe-sidebar', '.pd-recipe-sidebar-m');
			insertAfter('.header-contact', '.footer-col3');
			insertAfter('.wc-col-totals', '.ww-cart-header');
			insertBefore('.free-delivery-container', '.ww-cart-btns');
			insertAfter('.w-btn-change', '.btn-toggle-cart');
		},
		leave: () => {
			window.location.reload();
		},
	});

	function wrapBtnTextInSpan() {
		const buttons = document.querySelectorAll('.btn')

		buttons.forEach(btn => {
			const hasSpan = Array.from(btn.childNodes).some(
				node => node.nodeType === Node.ELEMENT_NODE && node.tagName === 'SPAN'
			)

			if (!hasSpan) {
				const textNodes = Array.from(btn.childNodes).filter(
					node => node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== ''
				)

				if (textNodes.length > 0) {
					const span = document.createElement('span')
					textNodes.forEach(textNode => span.appendChild(textNode))
					btn.insertBefore(span, btn.firstChild)
				}
			}
		})
	}

	let observer

	onMounted(() => {
		wrapBtnTextInSpan()

		observer = new MutationObserver(() => {
			wrapBtnTextInSpan()
		})

		observer.observe(document.body, {
			childList: true,
			subtree: true
		})
	})

	onBeforeUnmount(() => {
		if (observer) observer.disconnect()
	})


	// Cookiebot
	const language = lang.get();
	let scriptId = '';
	if(language == 'hr'){
		scriptId = '0678daa3-47bb-4d6a-90d7-fc702811b908';
	}else if(language == 'si'){
		scriptId = '7fd78573-b58d-4c38-a9cb-b227f90a9fd9';
	}else if(language == 'en'){
		scriptId = 'e3663285-1e6a-4ee0-a89f-346c385cb81e';
	}else if(language == 'de'){
		scriptId = '17e36d90-e6b4-432c-a6d4-5302ed315656';
	}
	addScript({
		key: 'Cookiebot',
		src: 'https://consent.cookiebot.com/uc.js',
		dataAttributes: {
			'cbid': scriptId,
			'consentmode': 'disabled'
		},
		async: true
	})

	// Zopim live chat
	addScript({
		key: 'zopim',
		src: 'https://static.zdassets.com/ekr/snippet.js?key=377b60e3-102e-4c2e-ad35-0b90b9070c1a',
		id: 'ze-snippet',
		delay: true,
	});
	waitForWindowProperty(
		'zE',
		zE => {
			addScript({
				key: 'zopimconfig',
				innerHTML: `zE('webWidget', 'setLocale', '${lang.get()}');window.zESettings = {webWidget: {color: {theme: '#ABC075',launcherText: '#FFFFFF'},launcher: {label: {'*': 'Korisnička podrška'}},chat: {title: {'*': 'Pomoć'},offlineForm: {greeting: {'*': 'Pomoć nije dostupna. Molimo kontaktirajte nas putem emaila ili telefona.',}}},position: {'horizontal': 'left'}}};`,
				delay: true,
			})
		},
	);

	addScript({
		key: 'mcjs',
		innerHTML: `!function(c,h,i,m,p){m=c.createElement(h),p=c.getElementsByTagName(h)[0],m.async=1,m.src=i,p.parentNode.insertBefore(m,p)}(document,"script","https://chimpstatic.com/mcjs-connected/js/users/e3ddb1a991ccc8888b24de8ee/8d73a2fdf5834aa3f6f385439.js");`,
		id: 'mcjs',
		delay: true,
	});

	/*
	addScript({
		key: 'zopim',
		innerHTML: `(function(h,o,t,j,a,r){
				h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
				h._hjSettings={hjid:911266,hjsv:6};
				a=o.getElementsByTagName('head')[0];
				r=o.createElement('script');r.async=1;
				r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
				a.appendChild(r);
			})(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`,
		id: 'zopim',
		delay: true,
	});
	*/
</script>

<style lang="less">
	a#CybotCookiebotDialogPoweredbyCybot,div#CybotCookiebotDialogPoweredByText {display: none;}
	#CookiebotWidget .CookiebotWidget-body .CookiebotWidget-main-logo {display: none;}
</style>

<style lang="less" scoped>
	:deep(.base-modal-gallery){background: #fff;}
	:deep(.base-modal-gallery-nav-btn){
		display: flex; align-items: center; justify-content: center;
		&:before{.icon-arrow-right(); font: 40px/1 @fonti; color: @black; .rotate(180deg); .transition(color);}
		@media (min-width: @h){
			&:hover:before{color: @lightGreen;}
		}
	}
</style>
