<template>
	<BaseForm name="shipping" @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}">
		<slot :errors="errors" :meta="meta" :values="values" :fields="fields" :onShippingUpdate="onShippingUpdate" :selectedShipping="selectedShipping" :activeShipping="activeShipping" :loading="loading" :status="status" />
	</BaseForm>
</template>

<script setup>
	const config = useAppConfig();
	const router = useRouter();
	const auth = useAuth();
	const webshop = useWebshop();
	const endpoints = useEndpoints();
	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const cartData = computed(() => webshop.getCartData());
	const {getAppUrl} = useApiRoutes();
	const emit = defineEmits(['load', 'update', 'submit']);
	const props = defineProps({
		parcel: Object,
		submitUrl: {
			type: String,
			default: 'webshop_payment',
		},
		onlyZipcodeAllowed: {
			type: Boolean,
			default: false,
		},
	});

	// emit event when step is loaded. Can be used to trigger analytics event or similar
	onMounted(() => emit('load'));

	const loading = ref(false);
	const status = ref(null);

	// if parcel prop is not provided (there are no multiple parcels), get first parcel from the cart
	const parcel = computed(() => {
		// return parcel prop if provided
		if (props.parcel) {
			return props.parcel;
		}

		// return first parcel from the cart and set selected shipping
		if (cartData.value?.parcels) {
			const p = cartData.value.parcels[0];
			return p;
		}

		return null;
	});

	// currently active (clicked) shipping option
	const activeShipping = ref(null);

	// selected shipping
	const selectedShipping = computed(() => {
		if (parcel.value?.shipping?.selected?.id) {
			activeShipping.value = parcel.value.shipping.selected;
			return parcel.value.shipping.selected;
		}

		return null;
	});

	// shipping form data
	const fields = computed(() => {
		if (!parcel.value?.shipping?.available?.length) return [];
		let fields = [];

		// filter only zipcode allowed shipping options
		if (parcel.value?.shipping?.available?.length) {
			fields = !props.onlyZipcodeAllowed ? parcel.value.shipping.available : parcel.value.shipping.available.filter(item => item.zipcode_allowed);
		}

		// get shipping options for each parcel
		if (fields.length) {
			return [
				{
					name: `parcel_${parcel.value.number}_shipping`,
					type: 'radio',
					options: fields.map(item => {
						return {
							...item,
							key: item.id,
							title: item.title,
							selected: selectedShipping.value?.id == item.id,
						};
					}),
					validation: [
						{
							type: 'not_empty',
							value: null,
							error: 'error_not_empty',
						},
					],
				},
			];
		}
	});

	// get shopping cart codes (products) for the parcel
	const shoppingCartCodes = computed(() => {
		return parcel.value.items.map(item => item.shopping_cart_code);
	});

	// save new shipping data
	let statusTimeout;
	const selectedPickupLocation = ref(null);
	async function onShippingUpdate(field, event = null) {
		activeShipping.value = field;
		status.value = null;
		if (statusTimeout) clearTimeout(statusTimeout);

		// set selected pickup location data if field widget is set to 'location'
		if (field.widget == 'location') {
			// if pickup location is not selected from the menu, stop here to prevent auto submit
			if (!selectedPickupLocation.value) return false;

			if (selectedPickupLocation.value) {
				field.shipping_data = {
					location_point_id: Number(selectedPickupLocation.value.id),
				};
			}
		} else {
			// reset selected pickup location if field is not location
			selectedPickupLocation.value = null;
		}

		// Prevent auto submit if field widget is set to 'gls_locker', 'gls_locker2' or 'boxnow_locker' and event is not provided (locker is not selected)
		if (['gls_locker', 'gls_locker2', 'boxnow_locker'].includes(field.widget) && !event) return false;

		// FIXME INTEG dodati mogućnost odabira datuma dostave za dostavnu službu
		//if (field?.shipping_date) field.shipping_date = field.shipping_date;

		loading.value = true;

		// update selected shipping and refresh cart
		const fetchOptions = {
			shipping_id: field.id,
			shopping_cart_codes: shoppingCartCodes.value,
			shipping_data: field.shipping_data ? field.shipping_data : null,
		};
		const res = await webshop.updateShipping(fetchOptions, {fetchCart: false});

		// Set selected parcel locker component (BoxNow or GLS). Must be done after shipping is updated and before cart is fetched
		if (['gls_locker', 'gls_locker2', 'boxnow_locker'].includes(field.widget) && event) {
			await useApi(endpoints.get('_post_hapi_webshop_parcel_locker'), {
				method: 'POST',
				body: event,
			});
		}

		await webshop.fetchCart();

		// gtm tracking
		if (gtm) {
			gtm.gtmTrack('addShippingInfo', {
				cart: cartData.value,
				shipping_tier: selectedShipping.value?.code,
			});
		}

		// emit event when shipping is updated. Can be used to trigger analytics event or similar
		emit('update', res);

		// set shipping status and reset after 5 seconds
		status.value = res;
		statusTimeout = setTimeout(() => {
			status.value = null;
		}, 5000);
		loading.value = false;
	}

	// redirect to next step
	async function onSubmit({values}) {
		if (!loading.value && !status.value?.data?.errors?.length && props.submitUrl) {
			loading.value = true;

			emit('submit', values);
			return navigateTo(getAppUrl(props.submitUrl));
		}
	}

	provide('baseWebshopShippingFormData', {
		parcel,
		selectedPickupLocation,
	});
</script>
