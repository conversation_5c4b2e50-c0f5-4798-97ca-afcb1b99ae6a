<template>
	<BasePublishCategory :root-category="true" :include-subcategories="true" v-slot="{item: category, rootCategory, contentType}" :seo="true">
		<Body class="page-publish-index page-instashop" :class="{'white-bg page-search': contentType == 'search'}" />
		<div class="wrapper wrapper-instashop">
			<div class="instashop-header">
				<div class="bc bc-instashop">
					<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
				</div>
				<BaseCmsLabel code="instashop_title" tag="h1" class="instashop-title" />
			</div>

			<BasePublishPosts :fetch="{extra_data: ['images']}" v-slot="{items: posts, nextPage, loadMore, loading}">
				<div class="instashop-items" :id="'items_'+category?.code" v-if="posts?.length">
					<PublishIndexEntryInstashop :items="posts" />
				</div>

				<ClientOnly>
					<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
						<button type="button" class="btn noarrow load-more btn-load-more" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel tag="span" code="load_more" /></button>
					</div>
				</ClientOnly>
				<BaseUiPagination class="pagination" />
			</BasePublishPosts>
		</div>

		<PublishInstashopModal :instashopData="instashopData" />
	</BasePublishCategory>
</template>
