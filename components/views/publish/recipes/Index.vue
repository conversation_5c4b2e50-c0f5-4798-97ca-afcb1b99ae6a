<template>
	<Body class="page-publish-index page-recipes" :class="{'white-bg page-search': contentType == 'search', 'active-filter': publishActiveFilters}" />
	<BasePublishCategory :root-category="true" :include-subcategories="true" v-slot="{item: category, rootCategory, contentType}" :seo="true">
		<BasePublishPosts v-slot="{items: posts, nextPage, loadMore, loading}">
			<div class="wrapper">
				<div class="df p-recipes-row">
					<BaseCmsRotator @loadRotatorItems="onLoad" :fetch="{code: 'hello_bar', limit: 1}" v-slot="{items: helloBarItems}">
						<aside class="p-recipes-sidebar" :class="{'recipes-sidebar-hellobar' : helloBarItems?.length}">
							<ClientOnly>
								<BasePublishFilters v-slot="{searchFields, selectedFiltersCounter}">
									<div class="cf cf-recipes" ref="cfRef">
										<div class="cf-filter-header" @click="closeFilters" v-if="mobileBreakpoint">
											<span><span class="filter-icon"></span><BaseCmsLabel code="filters" /></span>
											<span class="cf-filter-close"><span class="cf-filter-close-icon"></span></span>
										</div>
										<div class="cf-body">
											<BasePublishActiveFilters v-slot="{items, onRemove}">
												<div class="cf-active" v-show="items?.length">
													<BaseCmsLabel class="cf-active-title" code="selected_filters" tag="div" />
													<template v-for="item in items" :key="item.id">
														<div class="cf-active-item">
															<span class="cf-active-item-link" @click="onRemove(item)">{{item.title}}</span>
														</div>
													</template>
													<div class="cf-active-btns">
														<BaseCmsLabel class="btn-cf-active-clear" code="clear_filtering" tag="span" @click="onRemove()" />
													</div>
												</div>
												<div class="cf-btns">
													<div class="btn btn-gray btn-m-filter btn-m-cf-active-clear cf-btn-clear" v-if="items?.length" @click="onRemove(), closeFilters()"><BaseCmsLabel code="clear_active_filters" /></div>
													<div class="btn cf-btn-bottom cf-btn-confirm" @click="closeFilters"><BaseCmsLabel tag="span" code="filters_confirm" /></div>
												</div>
											</BasePublishActiveFilters>
											<div class="cf-items">
												<BasePublishFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" v-slot="{fields, onToggle, totalFields, onFilter, onClear, selectedFilters, active}">
													<template v-if="filter.code != 'category_id'">
														<div class="cf-item" :class="['cf-item-' + filter.code, {'active': !active}]">
															<div class="cf-title" :class="['cf-title-' + filter.code]" @click="onToggle">
																{{filter.label}}
																<span class="toggle-icon"></span>
															</div>
															<div class="cf-item-wrapper">
																<div class="cf-row" v-for="field in fields" :key="field.id">
																	<input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
																	<label :for="field.unique_code">
																		{{field.title}}
																		<span class="cf-counter">{{ field.total_available }}</span>
																	</label>
																</div>
															</div>
														</div>
													</template>
												</BasePublishFilterItem>
											</div>
										</div>
									</div>
								</BasePublishFilters>
							</ClientOnly>
						</aside>
					</BaseCmsRotator>
					<!-- <div class="cf-clear" @click="onClear" style="display: none;"></div> -->

					<div class="p-recipes-main" ref="recipesRef" v-if="posts?.length">
						<div class="p-items">
							<PublishIndexEntryRecipes v-for="post in posts" :key="post.id" :item="post" />
						</div>
						<ClientOnly>
							<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
								<button type="button" class="btn load-more btn-load-more btn-load-more-recipes" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel tag="span" code="load_more_recipes" /></button>
							</div>
						</ClientOnly>
						<BaseUiPagination class="pagination" />
					</div>
					<div v-else class="p-empty" ref="recipesRef"><BaseCmsLabel code="no_publish" /></div>
				</div>
			</div>
		</BasePublishPosts>
	</BasePublishCategory>
</template>

<script setup>
	const props = defineProps(['contentType']);
	const {mobileBreakpoint} = inject('rwd');
	//const {b2b} = useProfile();

	//Provjera za klasu za hellobar kod p-recipes-sidebar
	const isHidden = ref(true);
	let rotatorItemId = 0;
	function onLoad(data) {
		rotatorItemId = data?.items?.[0]?.id;
		const cookie = useCookie('hellobar_new');
		if(!cookie.value) {
			isHidden.value = false;
		}
	}
	const publishActiveFilters = useState('publishActiveFilters', () => false);
	const closeFilters = () => {
		publishActiveFilters.value = false;
	};

	const cfRef = ref(null)
	const recipesRef = ref(null)
	let observer = null

	const updateRecipesHeight = () => {
		if (cfRef.value && recipesRef.value) {
			const cfHeight = cfRef.value.offsetHeight
			recipesRef.value.style.minHeight = `${cfHeight - 180}px`
		}
	}

	onMounted(async () => {
		await nextTick()

		updateRecipesHeight()

		// observer
		observer = new ResizeObserver(() => {
			updateRecipesHeight()
		})

		if (cfRef.value) {
			observer.observe(cfRef.value)
		}
	})

	onBeforeUnmount(() => {
		if (observer && cfRef.value) {
			observer.unobserve(cfRef.value)
		}
	})
</script>

<style scoped lang="less">
	.cf-row{
		input[type=checkbox]+label{white-space: nowrap; font-size: 12px; width: 100%; padding-right: 30px;}
		input[type=checkbox]:checked+label{font-weight: bold;}
		&.manufacturer{
			input[type=checkbox]+label{
				padding: 6px 12px; border: 1px solid #DEDEDE; border-radius: 2px;
				&:before{display: none;}
			}
			input[type=checkbox]:checked+label{background: @lightGreen; border-color: @lightGreen; color: #fff; font-weight: normal;}
		}
	}
</style>
