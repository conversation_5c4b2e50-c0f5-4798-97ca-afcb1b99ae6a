<template>
	<Body class="page-publish-detail page-recipe-detail white-bg" />
	<BasePublishDetail :root-category="true" v-slot="{item}">
		<div class="df wrapper pd-recipe-wrapper">
			<div class="pd-recipe-main">
				<div class="pd-recipe-image">
					<span><BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width1000-height510-crop1']" default="/images/no-image-1000.jpg" :alt="item.main_image_description || item.seo_h1" :title="item.main_image_title" /></span>
				</div>
				<div class="pd-recipe-sidebar-m"></div>
				<div class="lists custom-list pd-recipe-cnt">
					<div class="df pd-recipe-info">
						<NuxtLink class="pd-all-articles" :to="item.root_category.url_without_domain" v-if="item.root_category"><BaseCmsLabel code="all_recipes" /></NuxtLink>
						<div class="pd-recipe-date"><BaseUtilsFormatDate :date="item.datetime_published" format="DD. MMMM YYYY." /></div>
					</div>
					<div v-if="item.content" v-html="item.content" v-interpolation ref="content"></div>
					<ul class="pd-documents" v-if="item.documents?.length">
						<li v-for="document in item.documents" :key="document.id">
							<a :href="document.url" :title="document.title + (document.description ? ' - ' + document.description : '')" target="_blank">{{ document.title || document.url }}</a>
						</li>
					</ul>
					<ClientOnly>
						<BaseUiImages v-if="item?.images?.length > 1" :images="item.images" v-slot="{items: images}">
							<BaseUiSwiper class="pd-thumbs" v-if="images" :options="sliderOptions">
								<BaseUiSwiperSlide v-for="file in images.slice(1)" :key="file.id">
									<a class="fancybox" rel="gallery" :title="file.title + (file.description ? ' - ' + file.description : '')">
										<BaseUiImage loading="lazy" :data="file.file_thumbs?.['width740-height500']" default="/images/no-image-500.jpg" :title="item.title" :alt="item.description ? item.description : item.title" />
									</a>
								</BaseUiSwiperSlide>
							</BaseUiSwiper>
						</BaseUiImages>
					</ClientOnly>
					<PublishTags v-if="item?.seo_keywords_tags" :itemSeoKeywordTags="item.seo_keywords_tags" mode="recipes" />
					<CmsShare mode="recipesDetail" />

					<div class="comments pd-recipe-comments" id="comments" v-if="item.feedback_comment_widget">
						<ClientOnly>
							<FeedbackCommentsForm :item="item" mode="pdRecipes" />
						</ClientOnly>
					</div>

					<BasePublishPostsWidget :fetch="{related_item_id: item.id, category_code: 'recipe', related_code: 'related', limit: 6, extra_fields: ['short_description']}" v-slot="{items: relatedRecipes}">
						<BasePublishPostsWidget :fetch="{related_tags_ids: item.seo_keywords_tags, id_exclude: item.id, category_code: 'recipe', sort: 'related_tag_total', limit: 6, extra_fields: ['short_description']}" v-slot="{items: relatedRecipesTags}">
							<BasePublishPostsWidget :fetch="{id_exclude: item.id, category_code: 'recipe', limit: 6, extra_fields: ['short_description']}" v-slot="{items: relatedRecipesOther}">
								<div v-if="relatedRecipes.length || relatedRecipesTags.length || relatedRecipesOther.length" class="pd-recipe-related">
									<BaseCmsLabel tag="div" class="pd-recipe-related-title" code="recipe_related" />
									<div class="pd-recipe-related-items">
										<template v-if="relatedRecipes.length">
											<PublishIndexEntryRecipes v-for="relatedRecipe in relatedRecipes" :key="relatedRecipe.id" :item="relatedRecipe" mode="list" />
										</template>
										<template v-else-if="relatedRecipesTags.length">
											<PublishIndexEntryRecipes v-for="relatedRecipe in relatedRecipesTags" :key="relatedRecipe.id" :item="relatedRecipe" mode="list" />
										</template>
										<template v-else-if="relatedRecipesOther.length">
											<PublishIndexEntryRecipes v-for="relatedRecipe in relatedRecipesOther" :key="relatedRecipe.id" :item="relatedRecipe" mode="list" />
										</template>
									</div>
								</div>
							</BasePublishPostsWidget>
						</BasePublishPostsWidget>
					</BasePublishPostsWidget>
				</div>
			</div>
			<aside class="pd-recipe-sidebar">
				<BaseCatalogProductsWidget :fetch="{related_publish_id: item.id, limit: 30}" v-slot="{items: relatedProducts}" v-model="relatedProductsRef" :gtm-tracking="{item_list_id: labels.get('used_products'), item_list_name: labels.get('used_products')}">
					<div class="pd-recipe-sidebar-top">
						<div class="rcd-attributes" v-if="item.attributes.length > 0">
							<div v-if="item.attributes_summary && item.attributes_summary.vrijeme_pripreme" class="pd-recipe-attr pd-recipe-attr-time">
								<span class="pd-recipe-attr-title">{{ item.attributes_summary.vrijeme_pripreme.title }}: </span>
								<span class="pd-recipe-attr-value">{{ item.attributes_summary.vrijeme_pripreme.content }}</span>
							</div>

							<template v-if="item.attributes">
								<div v-for="attr in item.attributes.filter(a => a.attribute_code === 'slozenost')" :key="attr.id" class="pd-recipe-attr pd-recipe-attr-complexity" :class="{'last': !item.element_ingredients || item.element_ingredients.length === 0}">
									<span class="pd-recipe-attr-image">
										<BaseUiImage loading="lazy" :src="attr.image_upload_path" default="/images/no-image-50.jpg" width="30" height="30" :alt="attr.title ? attr.title : ''" />
									</span>
									<span class="pd-recipe-attr-title">{{ attr.attribute_title }}</span>
									<span class="pd-recipe-attr-value"> {{ attr.title }}</span>
								</div>
							</template>

							<div class="pd-recipe-attr pd-recipe-attr-ingredients" v-if="item.element_ingredients">
								<div class="pd-recipe-attr-title pd-recipe-attr-ingredients-title">
									<BaseCmsLabel code="ingredients" />
									<template v-if="item.attributes_summary && item.attributes_summary.broj_sastojaka">
										<span class="pd-recipe-attr-counter"> ({{ item.attributes_summary.broj_sastojaka.content }})</span>
									</template>
								</div>
								<div v-if="item.element_ingredients" v-html="item.element_ingredients" v-interpolation ref="content"></div>
							</div>
						</div>
						<NuxtLink v-if="relatedProducts" class="btn btn-orange btn-show-related" href="#related-products"
							><span><BaseCmsLabel code="show_used_products" /></span
						></NuxtLink>
					</div>

					<div class="pd-recipe-related-products" id="related-products" v-if="relatedProducts">
						<BaseCmsLabel class="pd-recipe-related-products-title" code="used_products" tag="div" />
						<div class="pd-recipe-products">
							<!-- FIXME - provjeriti koji se još parametri moraju proslijediti u catalogIndexEntry
							<?php echo View::factory('catalog/index_entry', ['items' => $related_products, 'mode' => 'list', 'class' => 'cp-related', 'list' => 'Recepti', 'item_list_name' => Arr::get($cmslabel, 'used_products', 'Koristili smo'), 'item_list_id' => 'used_products']); ?> -->
							<CatalogIndexEntry :list="{'item_list_name': labels.get('used_products'), 'item_list_id': labels.get('used_products')}" v-for="relatedProduct in relatedProducts" :key="relatedProduct.id" :item="relatedProduct" class="cp-related" mode="list" list="Recepti" />
						</div>
						<BaseWebshopAddToCart :data="addToCartRelatedProducts" v-slot="{onAddToCart, loading}">
							<div class="pd-recipe-products-btns">
								<button :class="['btn btn-orange btn-add-all', {'loading': loading}]" @click="onAddToCart">
									<UiLoader v-if="loading" />
									<BaseCmsLabel code="all_to_cart" tag="span" />
								</button>
							</div>
						</BaseWebshopAddToCart>
					</div>
				</BaseCatalogProductsWidget>
			</aside>
		</div>
	</BasePublishDetail>
</template>

<script setup>
	const {onMediaQuery, appendTo, prependTo, insertAfter, insertBefore} = useDom();
	const {formatDate} = useText();
	const labels = useLabels();
	const props = defineProps(['mode']);

	const relatedProductsRef = ref([]);
	const addToCartRelatedProducts = computed (() => {
		if(!relatedProductsRef.value.length) return [];
		return relatedProductsRef.value.map(item => {
			return{
				modalData: item,
				shopping_cart_code: item.shopping_cart_code,
				quantity: item.qty,
			};
		});
	});

	/* onMediaQuery({
		query: '(max-width: 950px)',
		enter() {
			insertBefore('.pd-recipe-related-products', '.pd-recipe-related');
		},
		leave() {
			appendTo('.pd-recipe-related-products', '.pd-recipe-sidebar');
		},
	}); */

	/* onMediaQuery({
		query: '(max-width: 900px)',
		enter: () => {
			insertAfter('.pd-recipe-header-wrapper', '.header');
			appendTo('.pd-recipe-info', '.pd-recipe-info-container');
			appendTo('.pd-recipe-sidebar', '.pd-recipe-sidebar-m');
		},
		leave() {
			appendTo('.pd-recipe-header-wrapper', '.header');
			prependTo('.pd-recipe-info', '.pd-recipe-cnt');
			insertAfter('.pd-recipe-sidebar', '.pd-recipe-main');
		},
	}); */
</script>

<style scoped lang="less">
	.pd-recipe-attr-complexity {
		.pd-recipe-attr-title {
			padding-right: 3px;
		}
	}
</style>
