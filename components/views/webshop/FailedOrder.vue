<template>
	<BaseCmsPage v-slot="{page}">
		<CmsTwoColumns>
			<template #main>
				<div class="fg1 lists main-content">
					<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" v-html="page.content" v-interpolation />
				</div>
			</template>

			<template #sidebar>
				<CmsSidebar />
			</template>
		</CmsTwoColumns>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	.main{
		padding: 50px 0;
		@media (max-width: @t){padding: 30px 0;}
	}
	:deep(p){
		padding-bottom: 12px;
		@media (max-width: @t){padding-bottom: 10px;}
	}
</style>
