<template>
	<Body class="page-webshop page-webshop-payment page-checkout" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<div class="wc-col wc-col1 wc-step3-col1">
					<WebshopStep :step="1" :completed="true" />
					<div class="wc-col-cnt">
						<BaseWebshopCheckout v-slot="{customer, urls, formsMeta}">
							<div v-html="page.content" v-if="page?.content" v-interpolation />
							<BaseCmsLabel code="step2" tag="h1" class="wc-title" />
							<BaseWebshopShippingForm class="step3 step-form3 form form-animated-label ajax_siteform ajax_siteform_loading" v-slot="{fields, onShippingUpdate, loading, status, activeShipping}" submitUrl="webshop_review_order">
								<div v-if="status?.data?.errors">
									<div class="error global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<div class="shipping-options">
									<BaseCmsLabel code="checkout_shipping" tag="h2" class="wc-subtitle" v-interpolation />
									<template v-for="field in fields" :key="field.value">
										<BaseFormField :item="field" v-slot="{errorMessage}">
											<BaseFormInput :id="field.code" :option-class="'shipping-row'">
												<template #default="{option, updateFormValue}">
													<input type="radio" :name="field.name" :id="'shipping-'+option.id" :value="option.id" :checked="option.selected" @change="updateFormValue" @click="onShippingUpdate(option)" />
													<label :for="'shipping-'+option.id">
														<span v-html="option.title"></span>
														<BaseUiImage v-if="option?.image_upload_path" loading="lazy" :src="option.image_upload_path" default="/images/no-image-100.jpg" :alt="option.title ? option.title : ''" />
													</label>
													<div v-if="option.description" class="shipping_info" v-html="option.description"></div>
													<div class="shipping-note">
														<template v-if="option.code == 'dostavna_sluzba'">
															<div class="shipping-address">
																<span>{{customer?.first_name}} {{customer?.last_name}}</span>
																<span>{{customer?.address}}</span>
																<span>{{customer?.zipcode}} {{customer?.city}} {{customer?.country_name}}</span>
																<span>{{customer?.phone}}</span>
																<span>{{customer?.email}}</span>
																<NuxtLink :to="urls.webshop_customer" class="btn-change-address"><BaseCmsLabel code="change_shipping_address" /></NuxtLink>
															</div>
														</template>

														<LazyBaseThemeWebshopGlsParcelLockers v-if="['gls_locker', 'gls_locker2'].includes(option.widget) && activeShipping.id == option.id" @select="onShippingUpdate(option, $event)" />
														<LazyBaseThemeWebshopBoxNowParcelLockers v-if="['boxnow_locker'].includes(option.widget) && activeShipping.id == option.id" @select="onShippingUpdate(option, $event)" />
														<LazyBaseThemeWebshopWoltShipping :labels="{desired_delivery_time: 'wolt_delivery_time'}" v-if="option.code.startsWith('wolt_') && activeShipping.id == option.id" />
														<LazyBaseWebshopPickupLocations v-slot="{fields: locationFields, selectedLocation, onSelectLocation}" v-if="option.widget == 'location' && activeShipping.id == option.id">
															<div class="personal-pickup">
																<BaseFormField :item="locationFields[0]" v-slot="{errorMessage}">
																	<BaseFormInput type="select" id="field-shipping_pickup_location" @change="onSelectLocation($event), onShippingUpdate(option)" :value="selectedLocation?.id ? selectedLocation.id : ''">
																		<option v-for="option in locationFields" :key="option.name" :value="option.value">{{ option.title }}</option>
																	</BaseFormInput>
																	<span class="error" v-show="errorMessage" v-html="errorMessage" />
																</BaseFormField>
																<div class="shipping-location" v-if="selectedLocation">
																	<div class="shipping-location-address"><BaseCmsLabel code="address" tag="strong" />: <span class="address" v-if="selectedLocation.address" v-html="selectedLocation.address"></span></div>
																</div>
															</div>
														</LazyBaseWebshopPickupLocations>
													</div>
												</template>
											</BaseFormInput>
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</BaseFormField>
									</template>
								</div>
							</BaseWebshopShippingForm>

							<BaseWebshopPaymentForm v-slot="{fields, status, loading, onPaymentUpdate}" submitUrl="webshop_review_order">
								<div v-if="status?.data?.errors">
									<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
								</div>

								<div class="payment-options">
									<BaseCmsLabel code="checkout_payment" tag="h2" class="wc-subtitle" v-interpolation />
									<div class="field-payment">
										<template v-for="field in fields" :key="field.value">
											<BaseFormField :item="field" v-slot="{errorMessage}">
												<BaseFormInput :id="field.code" option-class="payment-row">
													<template #default="{option, updateFormValue}">
														<input type="radio" :name="field.name" :id="'payment-'+option.id" :value="option.id" :checked="option.selected" @change="updateFormValue" @click="onPaymentUpdate(option)" />
														<label :for="'payment-'+option.id">
															{{option.title}}
															<BaseUiImage v-if="option?.image_upload_path" loading="lazy" :src="option.image_upload_path" default="/images/no-image-100.jpg" :alt="option.title ? option.title : ''" />
														</label>
														<div v-if="option.description" class="payment_info" :data-payment_code="option.code">
															<span v-html="option.description"></span>
														</div>
													</template>
												</BaseFormInput>

												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</BaseFormField>
										</template>
									</div>
								</div>
								<div class="section payment-section">
									<button class="btn btn-checkout btn-orange" type="submit" :class="{'loading': loading}" :disabled="loading || !formsMeta?.allValid"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" tag="span" /></button>
								</div>
							</BaseWebshopPaymentForm>
						</BaseWebshopCheckout>
					</div>
					<WebshopStep :step="3" />
				</div>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	:deep(input[type=radio]+label){
		width: 100%;
	}
	:deep(.base-parcel-lockers-btn) span{cursor: pointer; color: @textColor;}
	:deep(.base-parcel-lockers-content){
		padding: 10px 0 0 0;
		p{padding: 0 0 2px;}
	}
	:deep(.base-wolt-shipping){
		input[type=checkbox]+label{color: @textColor;}
		.base-wolt-fields{padding: 10px 0 10px 30px;}
		select{min-width: 80px; padding: 0 40px 0 20px;}
	}
</style>
