<template>
	<Body :class="{'active': active}" />
	<BaseWebshopCartPreview v-slot="{parcels, cart, cartUrl}">
		<div class="ww-preview" :class="{'active': active}">
			<div class="ww-preview-header">
				<div class="ww-preview-title" :class="{'active': counter > 0}">
					<BaseCmsLabel code="in_cart" />
					<span class="ww-preview-count"
						>(<span class="cart_info_item_count">{{counter}}</span
						>)</span
					>
				</div>
				<slot name="closePreview" />
			</div>
			<div class="ww-preview-items-container" v-if="parcels[0]?.items?.length">
				<div class="ww-preview-items">
					<div class="ww-preview-table">
						<WebshopCartItemSmall v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
					</div>
				</div>
			</div>
			<div class="ww-preview-bottom">
				<WebshopCouponForm mode="preview" />
				<BaseWebshopPriorityOrder v-slot="{onSubmit, loading, isActive}">
					<div class="priority-order quick-priority-order" :class="[{'loading': loading}, props.mode]">
						<input type="checkbox" id="field-priority" :checked="isActive" @click.prevent="onSubmit" />

						<BaseCmsLabel for="field-priority" code="priority_order" tag="label" />
						<div v-if="loading" />
					</div>
				</BaseWebshopPriorityOrder>

				<WebshopLoyaltyPreview :cart="cart" :isGuestClientB2b="cart?.loyalty?.is_client_email_b2b" />

				<WebshopTotal mode="preview" simple="true" />

				<div class="ww-preview-buttons">
					<NuxtLink :to="cartUrl" class="btn btn-orange ww-btn-view">
						<BaseCmsLabel code="view_shopping_cart" tag="span" />
					</NuxtLink>
				</div>

				<BaseWebshopFreeShipping v-slot="{item}">
					<WebshopFreeDeliveryProgressBar :parcels="parcels" :toFree="item" />
				</BaseWebshopFreeShipping>
			</div>
		</div>
	</BaseWebshopCartPreview>
</template>

<script setup>
	const props = defineProps(['active', 'counter']);
</script>
