<template>
	<div class="wc-step step" :class="[{'wc-step-current': title == true},{'wc-step1': step === 1},{'wc-step2 step3': step === 2},{'wc-step3 step4 last-step': step === 3},{'completed-step': completed == true}]">
		<template v-if="title === true">
			<!--<span class="num">{{step}}</span>-->
			<BaseCmsLabel tag="span" :code="'step'+step" class="title" />
		</template>
		<template v-else>
			<BaseUtilsAppUrls v-slot="{items: appUrls}">
				<template v-if="step === 1">
					<NuxtLink class="step_link" :class="{'disabled': !completed}" :to="appUrls.webshop_customer">
						<BaseCmsLabel tag="span" code="step1" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="change-data" code="change_data" />
					</NuxtLink>
				</template>
				<template v-if="step === 2">
					<NuxtLink class="step_link" :class="{'disabled': !completed}" :to="appUrls.webshop_payment">
						<!--<span class="num">2</span>-->
						<BaseCmsLabel tag="span" code="step2" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="change-data" code="change_data" />
					</NuxtLink>
				</template>
				<template v-if="step === 3">
					<NuxtLink class="step_link" :class="{'disabled': !completed}" :to="appUrls.webshop_review_order">
						<BaseCmsLabel tag="span" code="step3" class="title" />
						<BaseCmsLabel v-if="completed == true" tag="span" class="change-data" code="change_data" />
					</NuxtLink>
				</template>
			</BaseUtilsAppUrls>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps({
		step: {
			type: null,
			default: false,
		},
		completed:{
			type: Boolean,
			default: false,
		},
		title:{
			type: Boolean,
			default: false,
		},
	});
</script>
