<template>
	<template v-if="!b2b && !isGuestClientB2b">
		<WebshopLoyalty v-slot="{onSubmit, loading, newIsActive, newCartTotal, newCartPercent}">
			<div v-if="newCartTotal && newCartPercent" class="w-loyalty" :class="{'checkout-new-loyalty': mode == 'checkout'}">
				<div class="loyalty-cnt" v-interpolation>
					<div v-html="labels.get('join_loyalty').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`)"></div>
				</div>
				<div class="loyalty-checkbox" :class="{'loading': loading}">
					<input type="checkbox" @click.prevent="onSubmit()" name="loyalty_request_new" id="field-loyalty_request_new" :checked="newIsActive" />
					<label for="field-loyalty_request_new" v-html="labels.get('join_loyalty_label').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`)"></label>
				</div>
			</div>
			<div v-else class="w-loyalty" :class="{'checkout-new-loyalty': mode == 'checkout'}">
				<div class="loyalty-checkbox" :class="{'loading': loading}">
					<input type="checkbox" @click.prevent="onSubmit()" name="loyalty_request_new" id="field-loyalty_request_new" :checked="newIsActive" />
					<label for="field-loyalty_request_new" v-html="labels.get('join_loyalty_new')"></label>
				</div>
			</div>
		</WebshopLoyalty>
	</template>
</template>

<script setup>
	const {mobileBreakpoint} = inject('rwd');
	const props = defineProps(['mode', 'isGuestClientB2b', 'cart']);
	const labels = useLabels();
	const {b2b} = useProfile();

	const {formatCurrency} = useCurrency();
</script>
