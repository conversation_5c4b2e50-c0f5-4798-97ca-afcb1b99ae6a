<template>
	<BaseCatalogLists :fetch="{code: 'checkout_suggestion', use_total: true, cache_lifetime: 60, response_fields: ['code', 'url_without_domain']}" v-slot="{items: list}">
		<!-- FIXME - pogledati da li je poziv dobar i zašto u hapi-u nema id_exclude te ovog skroz zadnjeg paramtera u pozivu "exclude_new"
					 PHP POZIV: <?php $free_delivery_list_items = Widget_Catalog::products(['lang' => $info['lang'], 'list_code' => $free_delivery_list['code'], 'limit' => 12, 'sort' => 'list_position', 'category_id' => $shopping_cart_categories, 'id_exclude' => array_values($shopping_cart_product_ids), 'exclude_new' => true]); ?> 
		-->
		<BaseCatalogProductsWidget
			:fetch="{list_code: list[0].code, sort: 'list_position', only_available: true, limit: 12, category_id: cart.product_categories, exclude_new: true}"
			v-slot="{items: products}"
			v-if="list?.length && toFree?.amount > 0 && cart?.product_categories?.length"
			:gtm-tracking="{item_list_id: 'checkout_suggestion', item_list_name: labels.get('free_delivery_widget_title')}">
			<div class="cart_info_total_extra_shipping_to_free_box free-delivery-widget active" :class="[mode]">
				<div class="fdw-title">
					<BaseCmsLabel code="free_delivery_widget_title" />
					<NuxtLink class="btn" :to="list[0].url_without_domain"><BaseCmsLabel code="show_all" tag="span" /></NuxtLink>
				</div>
				<div class="fdw-note" v-html="labels.get('free_delivery_widget_note').replace('%FREE_ABOVE%', formatCurrency(toFree.above))"></div>

				<div class="fdw-container">
					<div class="fwd-slider slick-carousel slick-arrow3 blazy-container">
						<CatalogSpecialLists :list="list[0]" :items="products" :perPage="3" />
					</div>
				</div>
			</div>
		</BaseCatalogProductsWidget>
	</BaseCatalogLists>
</template>

<script setup>
	const labels = useLabels();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['mode', 'cart', 'toFree', 'parcels']);

	/* function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "cart_delivery");
		}
	} */
</script>
