<template>
	<template v-if="!b2b && !isGuestClientB2b">
		<WebshopLoyalty v-slot="{onSubmit, loading, newIsActive, newCartTotal, newCartPercent, loyalty}">
			<div v-if="newCartTotal && newCartPercent" class="ww-loyalty">
				<input type="checkbox" @click.prevent="onSubmit()" name="loyalty_request_new" id="field-loyalty_request_new" :checked="newIsActive" />
				<label for="field-loyalty_request_new" v-html="labels.get('join_loyalty_label').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`)"></label>
			</div>
			<div v-else class="ww-loyalty">
				<input type="checkbox" name="loyalty_request_new" @click.prevent="onSubmit()" id="field-loyalty_request_new" :checked="newIsActive" />
				<label for="field-loyalty_request_new" v-html="labels.get('join_loyalty_new').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`)"></label>
			</div>
		</WebshopLoyalty>
	</template>
</template>

<script setup>
	const {mobileBreakpoint} = inject('rwd');
	const props = defineProps(['mode', 'isGuestClientB2b', 'cart']);
	const labels = useLabels();
	const {b2b} = useProfile();

	const {formatCurrency} = useCurrency();
</script>

<style lang="less" scoped>
	.ww-loyalty{
		input[type=checkbox]+label:before{top: 2px;}
	}
</style>
