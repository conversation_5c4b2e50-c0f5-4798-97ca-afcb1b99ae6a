<template>
	<template v-if="$route.meta.template === 'PublishAuthorDetail'">
		<BasePublishAuthors :use-slug="true" v-slot="{items}">
			<Title>{{ items[0]?.title }}</Title>
			<div class="wrapper wrapper-author-intro">
				<h1 class="p-author-title" v-if="items[0]?.title">{{ items[0]?.title }}</h1>
				<div class="p-author-desc" v-if="items[0]?.short_description" v-html="items[0]?.short_description" />
			</div>
		</BasePublishAuthors>
	</template>
	<template v-else>
		<Body :class="{'active-filter': publishActiveFilters}" />
		<div class="wrapper p-recipes-header">
			<CmsBreadcrumbs v-if="category?.breadcrumbs" class="p-recipes-bc" :items="category.breadcrumbs" />
			<h1 class="p-recipes-title">{{ category?.title }}</h1>
			<div class="p-recipes-description" v-html="category?.content" v-interpolation></div>
			<div class="btn-toggle-filter btn-toggle-recipe-filter" @click="toggleFilters">
				<span><span class="filter-icon"></span><BaseCmsLabel code="filters" /></span>
			</div>
		</div>
	</template>
</template>

<script setup>
	const category = useState('publishCategory');
	const publishActiveFilters = useState('publishActiveFilters', () => false);
	const toggleFilters = () => {
		publishActiveFilters.value = !publishActiveFilters.value;
	};
</script>
